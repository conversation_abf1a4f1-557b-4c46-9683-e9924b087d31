import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Purchase, ColumnVisibility } from "../types"; // Import from the types file
import { purchasesColumnConfig } from "../config/columnConfig";
import { Trash, Printer, Edit, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { deletePurchase } from "@/actions/entities/purchases";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Use shared column configuration
const columnConfig = purchasesColumnConfig;

interface PurchaseTableDesktopProps {
  purchases: Purchase[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  searchTerm: string;
  onSelectionChange?: (selectedIds: string[]) => void;
}

export const PurchaseTableDesktop: React.FC<PurchaseTableDesktopProps> = ({
  purchases,
  columnVisibility,
  handleSort,
  getSortIcon,
  searchTerm,
  onSelectionChange,
}) => {
  const router = useRouter();
  const [purchaseToDelete, setPurchaseToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedPurchases, setSelectedPurchases] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<string>("horizontal1");
  const [selectedOrientation, setSelectedOrientation] = useState<
    "horizontal" | "vertical"
  >("horizontal");
  const [currentPurchase, setCurrentPurchase] = useState<Purchase | null>(null);

  // Helper function to render cell content based on column key
  const renderCellContent = (
    purchase: Purchase,
    columnKey: keyof ColumnVisibility
  ) => {
    switch (columnKey) {
      case "id":
        return (
          <Link
            href={`/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`}
            className="hover:underline text-blue-600 dark:text-blue-400 cursor-pointer"
          >
            {purchase.transactionNumber || purchase.id.substring(0, 8)}
          </Link>
        );
      case "date":
        return formatDate(purchase.purchaseDate);
      case "paymentDueDate":
        return purchase.paymentDueDate
          ? formatDate(purchase.paymentDueDate)
          : "-";
      case "supplier":
        return purchase.supplier?.name || "-";
      case "totalAmount":
        return `Rp ${purchase.totalAmount.toLocaleString("id-ID")}`;
      case "itemCount":
        return purchase.items.length;
      case "quantity":
        return purchase.items
          .reduce((sum, item) => sum + (item.quantity || 0), 0)
          .toLocaleString("id-ID");
      case "invoiceRef":
        return purchase.invoiceRef || "-";
      case "tags":
        return purchase.tags && purchase.tags.length > 0
          ? purchase.tags.join(", ")
          : "-";
      default:
        return "-";
    }
  };

  // Handle select all checkbox
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      const allIds = purchases.map((purchase) => purchase.id);
      setSelectedPurchases(allIds);
      if (onSelectionChange) onSelectionChange(allIds);
    } else {
      setSelectedPurchases([]);
      if (onSelectionChange) onSelectionChange([]);
    }
  };

  // Handle individual checkbox selection
  const handleSelectPurchase = (purchaseId: string, checked: boolean) => {
    let newSelected: string[];

    if (checked) {
      newSelected = [...selectedPurchases, purchaseId];
    } else {
      newSelected = selectedPurchases.filter((id) => id !== purchaseId);
      // If we're deselecting an item, also uncheck the "select all" checkbox
      if (selectAll) setSelectAll(false);
    }

    setSelectedPurchases(newSelected);
    if (onSelectionChange) onSelectionChange(newSelected);
  };

  // Handle delete purchase
  const handleDeletePurchase = async (id: string) => {
    setIsDeleting(true);
    try {
      const result = await deletePurchase(id);
      if (result.success) {
        toast.success(result.success);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting purchase:", error);
      toast.error("Terjadi kesalahan saat menghapus pembelian.");
    } finally {
      setIsDeleting(false);
      setPurchaseToDelete(null);
    }
  };

  // Handle print invoice - open dialog for template selection
  const handlePrintInvoice = (purchase: Purchase) => {
    setCurrentPurchase(purchase);
    setIsPrintDialogOpen(true);
  };

  // Handle actual print after template selection
  const handlePrint = () => {
    if (!currentPurchase) return;

    // Close the dialog
    setIsPrintDialogOpen(false);

    // Create a new window for printing
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast.error(
        "Popup diblokir oleh browser. Mohon izinkan popup untuk mencetak faktur."
      );
      return;
    }

    // Import the template dynamically to avoid bundling all templates
    import("../../purchases/templates")
      .then(({ getInvoiceTemplate }) => {
        // Use the selected template directly
        const templateContent = getInvoiceTemplate(
          selectedTemplate as any,
          currentPurchase
        );

        // Write the template content to the new window using a more modern approach
        const parser = new DOMParser();
        const htmlDoc = parser.parseFromString(templateContent, "text/html");

        // Clear the document and append the new content
        printWindow.document.documentElement.innerHTML = "";
        Array.from(htmlDoc.head.childNodes).forEach((node) => {
          printWindow.document.head.appendChild(
            printWindow.document.importNode(node, true)
          );
        });
        Array.from(htmlDoc.body.childNodes).forEach((node) => {
          printWindow.document.body.appendChild(
            printWindow.document.importNode(node, true)
          );
        });

        // Wait for images and resources to load before printing
        printWindow.onload = () => {
          printWindow.print();
          // printWindow.close(); // Uncomment to auto-close after print dialog
        };
      })
      .catch((error) => {
        console.error("Error loading templates:", error);
        toast.error("Terjadi kesalahan saat memuat template faktur.");
        printWindow.close();
      });
  };

  // Format date for display in concise format (DD/MM/YYYY)
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, "0");
    const month = String(d.getMonth() + 1).padStart(2, "0"); // Month is 0-indexed
    const year = d.getFullYear();

    return `${day}/${month}/${year}`;
  };
  return (
    <div className="relative overflow-x-auto bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="sticky top-0 z-10 text-xs text-gray-700 dark:text-gray-300 uppercase bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:bg-gray-700">
          <tr>
            {/* Checkbox column - always visible */}
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              <Checkbox
                checked={selectAll}
                onCheckedChange={handleSelectAll}
                aria-label="Select all purchases"
              />
            </th>
            {columnConfig.map(
              (column) =>
                columnVisibility[column.key] && (
                  <th
                    key={column.key}
                    scope="col"
                    className="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 border-r border-gray-200 dark:border-gray-700"
                    onClick={() => handleSort(column.sortKey)}
                  >
                    <div className="flex items-center">
                      {column.label}
                      {getSortIcon(column.sortKey)}
                    </div>
                  </th>
                )
            )}
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {purchases.length > 0 ? (
            purchases.map((purchase) => (
              <tr
                key={purchase.id}
                className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                {/* Checkbox cell - always visible */}
                <td className="px-4 py-4 border-r border-gray-200 dark:border-gray-700">
                  <Checkbox
                    checked={selectedPurchases.includes(purchase.id)}
                    onCheckedChange={(checked) =>
                      handleSelectPurchase(purchase.id, checked === true)
                    }
                    aria-label={`Select purchase ${purchase.id}`}
                  />
                </td>
                {columnConfig.map(
                  (column) =>
                    columnVisibility[column.key] && (
                      <td
                        key={column.key}
                        className={`px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 ${
                          column.key === "id"
                            ? "font-medium text-gray-900 dark:text-gray-100"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {renderCellContent(purchase, column.key)}
                      </td>
                    )
                )}
                <td className="px-4 py-4 text-right whitespace-nowrap border-l border-gray-200 dark:border-gray-700">
                  <div className="flex justify-end space-x-1">
                    {/* Print Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-yellow-300 text-black hover:text-gray-700 cursor-pointer hover:bg-yellow-200"
                      onClick={() => handlePrintInvoice(purchase)}
                    >
                      <Printer className="h-4 w-4" />
                      <span className="sr-only">Print Invoice</span>
                    </Button>

                    {/* Share Button (WhatsApp) */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-green-500 text-white cursor-pointer hover:bg-green-400"
                      onClick={() => {
                        const message = `Pembelian ${purchase.transactionNumber || purchase.id} - Total: ${new Intl.NumberFormat(
                          "id-ID",
                          {
                            style: "currency",
                            currency: "IDR",
                            minimumFractionDigits: 0,
                          }
                        ).format(purchase.totalAmount)} - cek detail disini`;
                        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                        window.open(whatsappUrl, "_blank");
                      }}
                    >
                      <Share2 className="h-4 w-4" />
                      <span className="sr-only">Share via WhatsApp</span>
                    </Button>

                    {/* Edit Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-blue-500 text-white cursor-pointer hover:bg-blue-400"
                      onClick={() =>
                        router.push(`/dashboard/purchases/edit/${purchase.id}`)
                      }
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>

                    {/* Delete Button */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-red-500 text-white cursor-pointer hover:bg-red-400"
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus pembelian ini?
                            Tindakan ini tidak dapat dibatalkan dan akan
                            mengurangi stok produk.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              setPurchaseToDelete(purchase.id);
                              handleDeletePurchase(purchase.id);
                            }}
                            disabled={
                              isDeleting && purchaseToDelete === purchase.id
                            }
                            className="bg-red-500 hover:bg-red-600"
                          >
                            {isDeleting && purchaseToDelete === purchase.id
                              ? "Menghapus..."
                              : "Hapus"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={
                  Object.values(columnVisibility).filter(Boolean).length + 2 // +2 for checkbox and actions columns
                }
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {searchTerm
                  ? "Tidak ada pembelian yang sesuai dengan pencarian."
                  : "Belum ada data pembelian."}
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Print Dialog */}
      <Dialog open={isPrintDialogOpen} onOpenChange={setIsPrintDialogOpen}>
        <DialogContent className="max-h-[80vh] h-[80vh] overflow-y-auto max-w-[90vw] w-[900px] overflow-x-hidden">
          <DialogHeader>
            <DialogTitle>Cetak Faktur Pembelian</DialogTitle>
            <DialogDescription>
              Pilih template faktur yang ingin Anda cetak.
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-[300px_minmax(0,1fr)] gap-6 py-4">
            {/* Left Column - Options */}
            <div className="space-y-6 flex flex-col h-full">
              {/* Orientation Options */}
              <div className="space-y-3">
                <div className="font-medium">Jenis Faktur</div>
                <div className="grid grid-cols-2 gap-3">
                  <div
                    className={`border rounded-md p-3 cursor-pointer ${
                      selectedOrientation === "horizontal"
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      setSelectedOrientation("horizontal");
                      // Update template to match orientation
                      if (selectedTemplate.startsWith("vertical")) {
                        const styleNumber = selectedTemplate.replace(
                          "vertical",
                          ""
                        );
                        setSelectedTemplate(`horizontal${styleNumber}`);
                      }
                    }}
                  >
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1.5 text-blue-600"
                        >
                          <rect
                            x="3"
                            y="3"
                            width="18"
                            height="18"
                            rx="2"
                            ry="2"
                          ></rect>
                          <line x1="3" y1="9" x2="21" y2="9"></line>
                          <line x1="3" y1="15" x2="21" y2="15"></line>
                        </svg>
                        <span className="font-medium text-sm">
                          Faktur Lebar
                        </span>
                      </div>
                      <div className="w-full h-12 bg-white border border-gray-200 rounded-md flex items-center justify-center relative">
                        <div className="w-full h-full p-2 flex flex-col justify-center">
                          <div className="h-1.5 bg-gray-200 w-full mb-1 rounded"></div>
                          <div className="flex justify-between items-center">
                            <div className="w-1/3">
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                            </div>
                            <div className="w-3/5">
                              <div className="h-1 bg-gray-200 w-full mb-0.5 rounded"></div>
                              <div className="h-1 bg-gray-200 w-full rounded"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1.5">
                        Kertas A4
                      </div>
                    </div>
                  </div>

                  <div
                    className={`border rounded-md p-3 cursor-pointer ${
                      selectedOrientation === "vertical"
                        ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      setSelectedOrientation("vertical");
                      // Update template to match orientation
                      if (selectedTemplate.startsWith("horizontal")) {
                        const styleNumber = selectedTemplate.replace(
                          "horizontal",
                          ""
                        );
                        setSelectedTemplate(`vertical${styleNumber}`);
                      }
                    }}
                  >
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1.5 text-purple-600"
                        >
                          <rect
                            x="3"
                            y="3"
                            width="18"
                            height="18"
                            rx="2"
                            ry="2"
                          ></rect>
                          <line x1="8" y1="3" x2="8" y2="21"></line>
                          <line x1="16" y1="3" x2="16" y2="21"></line>
                        </svg>
                        <span className="font-medium text-sm">
                          Faktur Kecil
                        </span>
                      </div>
                      <div className="w-full h-12 bg-white border border-gray-200 rounded-md flex items-center justify-center relative">
                        <div className="w-full h-full p-2 flex flex-col justify-center">
                          <div className="h-1.5 bg-gray-200 w-full mb-1 rounded"></div>
                          <div className="flex flex-col items-center">
                            <div className="w-3/4">
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                            </div>
                            <div className="w-full">
                              <div className="h-1 bg-gray-200 w-full rounded"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1.5">
                        Thermal Printer (58mm/80mm)
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Template Style Options */}
              <div className="space-y-3">
                <div className="font-medium">Gaya Template</div>
                <Select
                  value={selectedTemplate}
                  onValueChange={(value) => {
                    setSelectedTemplate(value);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Pilih template" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedOrientation === "horizontal" ? (
                      <SelectGroup>
                        <SelectLabel>Template Faktur Lebar (A4)</SelectLabel>
                        <SelectItem value="horizontal1">Template 1</SelectItem>
                        <SelectItem value="horizontal2">Template 2</SelectItem>
                        <SelectItem value="horizontal3">Template 3</SelectItem>
                      </SelectGroup>
                    ) : (
                      <SelectGroup>
                        <SelectLabel>
                          Template Faktur Kecil (Thermal)
                        </SelectLabel>
                        <SelectItem value="vertical1">Template 1</SelectItem>
                        <SelectItem value="vertical2">Template 2</SelectItem>
                        <SelectItem value="vertical3">Template 3</SelectItem>
                      </SelectGroup>
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Action Buttons */}
              <div className="mt-auto pt-6">
                <div className="flex flex-col gap-3">
                  <Button onClick={handlePrint} className="w-full">
                    Cetak Sekarang
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setIsPrintDialogOpen(false)}
                    className="w-full"
                  >
                    Batal
                  </Button>
                </div>
              </div>
            </div>

            {/* Right Column - Preview */}
            <div className="border rounded-md p-4 overflow-hidden">
              <div className="font-medium mb-3 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 text-gray-500"
                >
                  <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                Pratinjau Template
              </div>
              <div
                className={`${
                  selectedOrientation === "horizontal"
                    ? "aspect-[16/9]"
                    : "aspect-[3/4]"
                } bg-gray-100 rounded-md flex items-center justify-center overflow-hidden border border-dashed border-gray-300 min-h-[400px] max-w-full`}
              >
                {/* Image placeholder for template preview */}
                <div className="w-full h-full relative flex flex-col items-center justify-center overflow-hidden">
                  {/* Template image will be placed here */}
                  <div className="text-gray-400 flex flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="64"
                      height="64"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mb-3"
                    >
                      <rect
                        x="3"
                        y="3"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <circle cx="8.5" cy="8.5" r="1.5"></circle>
                      <polyline points="21 15 16 10 5 21"></polyline>
                    </svg>
                    <span className="text-base">Pratinjau Template</span>
                    <span className="text-sm mt-1">
                      {selectedOrientation === "horizontal"
                        ? "Faktur Lebar (Kertas A4)"
                        : "Faktur Kecil (Thermal Printer 58mm/80mm)"}
                    </span>
                  </div>

                  {/* Overlay with template name */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white p-2 text-center">
                    <div className="font-medium text-sm">
                      {selectedTemplate.includes("horizontal1") && "Template 1"}
                      {selectedTemplate.includes("horizontal2") && "Template 2"}
                      {selectedTemplate.includes("horizontal3") && "Template 3"}
                      {selectedTemplate.includes("horizontal4") && "Template 4"}
                      {selectedTemplate.includes("vertical1") && "Template 1"}
                      {selectedTemplate.includes("vertical2") && "Template 2"}
                      {selectedTemplate.includes("vertical3") && "Template 3"}
                      {selectedTemplate.includes("vertical4") && "Template 4"}
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-3 text-center">
                <div className="font-bold">
                  {selectedOrientation === "horizontal"
                    ? "Faktur Lebar"
                    : "Faktur Kecil"}{" "}
                  -{selectedTemplate.includes("1") && " Template 1"}
                  {selectedTemplate.includes("2") && " Template 2"}
                  {selectedTemplate.includes("3") && " Template 3"}
                  {selectedTemplate.includes("4") && " Template 4"}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  Gambar pratinjau akan ditampilkan di sini
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
